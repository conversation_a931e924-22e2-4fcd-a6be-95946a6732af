plugins {
    id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file("key.properties")
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.ajory.quran_broadcast_app"
    compileSdk 35
    ndkVersion "28.0.13004108"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        multiDexEnabled true
        applicationId = "com.perfectfit.qurankareem"
        minSdk = 23
        targetSdk = 35
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
        }
    }


    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
        }
        release {
            minifyEnabled false
            shrinkResources false
//            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig = signingConfigs.release
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    // Essential for Java 8+ features on older Android versions
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'

    // Core AndroidX - Required by Flutter plugins (using stable versions)
    implementation 'androidx.core:core:1.12.0'
    implementation 'androidx.core:core-ktx:1.12.0'

    // Required by awesome_notifications
    implementation 'androidx.appcompat:appcompat:1.6.1'

    // Required by home_widget and UI components
    implementation 'com.google.android.material:material:1.11.0'

    // Google Play Core - Removed due to SDK 34 compatibility issues
    // implementation 'com.google.android.play:core:1.10.3'

    // Force consistent versions to prevent conflicts
    configurations.all {
        resolutionStrategy {
            force "androidx.core:core:1.12.0"
            force "androidx.core:core-ktx:1.12.0"
            force "androidx.appcompat:appcompat:1.6.1"
        }
    }
}