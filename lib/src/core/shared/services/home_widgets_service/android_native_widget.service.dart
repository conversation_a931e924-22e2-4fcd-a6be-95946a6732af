import 'dart:io';
import 'package:flutter/services.dart';
import 'package:xr_helper/xr_helper.dart';

class AndroidNativeWidgetService {
  static const MethodChannel _channel = MethodChannel('com.perfectfit.qurankareem/prayer_widget');
  
  /// Save all prayer times for Android widget (equivalent to iOS saveIOSAllPrayerTimesForWidget)
  static Future<bool> saveAndroidAllPrayerTimesForWidget() async {
    if (!Platform.isAndroid) {
      Log.w('saveAndroidAllPrayerTimesForWidget called on non-Android platform');
      return false;
    }
    
    try {
      Log.i('Calling native Android saveAndroidAllPrayerTimesForWidget');
      final result = await _channel.invokeMethod('saveAndroidAllPrayerTimesForWidget');
      Log.i('Android prayer times saved successfully: $result');
      return result ?? false;
    } catch (e) {
      Log.e('Error saving Android prayer times: $e');
      return false;
    }
  }
  
  /// Schedule Android prayer updates natively
  static Future<bool> scheduleAndroidPrayerUpdates() async {
    if (!Platform.isAndroid) {
      Log.w('scheduleAndroidPrayerUpdates called on non-Android platform');
      return false;
    }
    
    try {
      Log.i('Scheduling Android prayer updates natively');
      final result = await _channel.invokeMethod('scheduleAndroidPrayerUpdates');
      Log.i('Android prayer updates scheduled: $result');
      return result ?? false;
    } catch (e) {
      Log.e('Error scheduling Android prayer updates: $e');
      return false;
    }
  }
  
  /// Update Android widgets natively
  static Future<bool> updateAndroidWidgets() async {
    if (!Platform.isAndroid) {
      Log.w('updateAndroidWidgets called on non-Android platform');
      return false;
    }
    
    try {
      Log.i('Updating Android widgets natively');
      final result = await _channel.invokeMethod('updateAndroidWidgets');
      Log.i('Android widgets updated: $result');
      return result ?? false;
    } catch (e) {
      Log.e('Error updating Android widgets: $e');
      return false;
    }
  }
  
  /// Cancel all scheduled Android updates
  static Future<bool> cancelAndroidScheduledUpdates() async {
    if (!Platform.isAndroid) {
      Log.w('cancelAndroidScheduledUpdates called on non-Android platform');
      return false;
    }
    
    try {
      Log.i('Cancelling Android scheduled updates');
      final result = await _channel.invokeMethod('cancelAndroidScheduledUpdates');
      Log.i('Android scheduled updates cancelled: $result');
      return result ?? false;
    } catch (e) {
      Log.e('Error cancelling Android scheduled updates: $e');
      return false;
    }
  }
  
  /// Check if Android prayer data is available
  static Future<bool> hasAndroidPrayerData() async {
    if (!Platform.isAndroid) {
      Log.w('hasAndroidPrayerData called on non-Android platform');
      return false;
    }
    
    try {
      final result = await _channel.invokeMethod('hasAndroidPrayerData');
      Log.d('Android prayer data available: $result');
      return result ?? false;
    } catch (e) {
      Log.e('Error checking Android prayer data: $e');
      return false;
    }
  }
}
