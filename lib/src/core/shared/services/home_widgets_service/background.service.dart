import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:android_alarm_manager_plus/android_alarm_manager_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

/// The name associated with the UI isolate's [SendPort].
const String isolateName = 'prayer_widget_isolate';

/// Configuration constants for alarm management
class AlarmConfig {
  static const int maxDaysToSchedule = 2; // Schedule today + tomorrow
  static const int maxAlarmsAllowed = 15; // 6 prayers × 2 days + 3 buffer
  static const int midnightAlarmId = 999999; // ID for midnight refresh alarm
}

/// A port used to communicate from a background isolate to the UI isolate.
ReceivePort port = ReceivePort();

/// Initialize the background service
Future<void> initializeBackgroundService() async {
  if (Platform.isAndroid) {
    // Register the UI isolate's SendPort to allow for communication from the background isolate
    IsolateNameServer.registerPortWithName(
      port.sendPort,
      isolateName,
    );

    // Request exact alarm permission
    await Permission.scheduleExactAlarm.request();

    // Initialize Android Alarm Manager
    await AndroidAlarmManager.initialize();
  }
}

/// The background callback for prayer time updates
@pragma('vm:entry-point')
void prayerTimeUpdateCallback() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  Log.i('Background task started at ${DateTime.now()}');

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  Log.i('Current time: $now');
  Log.i('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    Log.i('Fetching next day data for Fajr: $nextDay');
    currentDayData = calendarController.calendarByDate(nextDay);
    prayerTimes = currentDayData.prayerTimes;
    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
    Log.i(
        'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);

  // Save widget data for both platforms
  if (Platform.isIOS) {
    await HomeWidgetService.saveIOSAllPrayerTimesForWidget();
  } else if (Platform.isAndroid) {
    await HomeWidgetService.saveAndroidAllPrayerTimesForWidget();
  }

  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  Log.i('Widget updated successfully');

  // Notify UI isolate if available
  final uiSendPort = IsolateNameServer.lookupPortByName(isolateName);
  uiSendPort?.send(null);

  // Schedule prayer updates using 2-day rolling approach
  try {
    if (Platform.isAndroid) {
      // Check if we need to schedule the next batch
      final shouldScheduleNext = await _shouldScheduleNextBatch();
      if (shouldScheduleNext) {
        await scheduleTwoDayRollingUpdates();
        Log.i(
            'Scheduled next 2-day batch due to approaching end of current batch');
      } else {
        Log.i('Current 2-day batch still has sufficient coverage');
      }
    }
  } catch (e) {
    Log.e('Error scheduling next update: $e');
  }

  Log.i('Background task completed at ${DateTime.now()}');
}

/// Schedule prayers for today and tomorrow (2-day rolling approach)
Future<void> scheduleTwoDayRollingUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Cancel existing alarms first
    await cancelAllScheduledAlarms();

    final calendarController =
        ProviderContainer().read(calendarControllerNotifierProvider);

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));

    int totalScheduled = 0;

    // Schedule remaining prayers for today
    totalScheduled +=
        await _schedulePrayersForDate(calendarController, today, 'today');

    // Schedule all prayers for tomorrow
    totalScheduled +=
        await _schedulePrayersForDate(calendarController, tomorrow, 'tomorrow');

    // Schedule midnight update for day after tomorrow to trigger next batch
    final dayAfterTomorrow = tomorrow.add(const Duration(days: 1));
    await _scheduleMidnightUpdate(dayAfterTomorrow);

    Log.i(
        '2-day rolling schedule completed: $totalScheduled prayers scheduled');
  } catch (e) {
    Log.e('Error in 2-day rolling updates: $e');
  }
}

/// Schedule prayers for a specific date
Future<int> _schedulePrayersForDate(CalendarController calendarController,
    DateTime date, String dayLabel) async {
  int scheduledCount = 0;

  try {
    final dayData = calendarController.calendarByDate(date);
    final prayerTimes = dayData.prayerTimes;
    final now = DateTime.now();

    final prayers = [
      {'name': 'fajr', 'time': prayerTimes.fajr},
      {'name': 'sunrise', 'time': prayerTimes.sunrise},
      {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
      {'name': 'asr', 'time': prayerTimes.asr},
      {'name': 'maghrib', 'time': prayerTimes.maghrib},
      {'name': 'isha', 'time': prayerTimes.isha},
    ];

    for (int i = 0; i < prayers.length; i++) {
      final prayer = prayers[i];
      final timeString = prayer['time'] as String;
      if (timeString.isEmpty) continue;

      try {
        final timeParts = timeString.split(':');
        if (timeParts.length < 2) continue;

        final hour = int.parse(timeParts[0]);
        final minute = int.parse(timeParts[1]);
        final prayerDateTime =
            DateTime(date.year, date.month, date.day, hour, minute);

        // Only schedule if the prayer time is in the future
        if (prayerDateTime.isAfter(now)) {
          final alarmId = _createAlarmId(date, i);

          await AndroidAlarmManager.oneShotAt(
            prayerDateTime,
            alarmId,
            prayerTimeUpdateCallback,
            exact: true,
            wakeup: true,
            rescheduleOnReboot: true,
          );

          scheduledCount++;
          Log.i('Scheduled ${prayer['name']} for $dayLabel at $timeString');
        }
      } catch (e) {
        Log.e('Error scheduling ${prayer['name']} for $dayLabel: $e');
      }
    }
  } catch (e) {
    Log.e('Error scheduling prayers for $dayLabel: $e');
  }

  return scheduledCount;
}

/// Create a unique alarm ID based on date and prayer index
int _createAlarmId(DateTime date, int prayerIndex) {
  // Create unique ID: YYMMDDHH where HH is prayer index (0-5)
  final year = date.year % 100; // Last 2 digits of year
  final month = date.month;
  final day = date.day;

  return (year * 1000000) + (month * 10000) + (day * 100) + prayerIndex;
}

/// Schedule midnight update for specified date
Future<void> _scheduleMidnightUpdate(DateTime targetDate) async {
  try {
    final midnightUpdate = DateTime(
        targetDate.year, targetDate.month, targetDate.day, 0, 1); // 00:01 AM

    await AndroidAlarmManager.oneShotAt(
      midnightUpdate,
      AlarmConfig.midnightAlarmId,
      prayerTimeUpdateCallback,
      exact: true,
      wakeup: true,
      rescheduleOnReboot: true,
    );

    Log.i('Scheduled midnight update for: $midnightUpdate');
  } catch (e) {
    Log.e('Error scheduling midnight update: $e');
  }
}

/// Schedule prayer updates using 2-day rolling approach (replaces old bulk scheduling)
Future<void> scheduleAllFuturePrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Use 2-day rolling scheduling instead of bulk scheduling
    await scheduleTwoDayRollingUpdates();
    Log.i('2-day rolling prayer scheduling initiated');
  } catch (e) {
    Log.e('Error scheduling future prayer updates: $e');
  }
}

// Removed old bulk scheduling function - replaced with sequential scheduling

/// Schedule daily prayer updates using 2-day rolling approach
Future<void> scheduleAllDailyPrayerUpdates() async {
  if (Platform.isIOS) return;

  try {
    // Use 2-day rolling scheduling instead of bulk scheduling
    await scheduleTwoDayRollingUpdates();
    Log.i('2-day rolling daily prayer scheduling completed');
  } catch (e) {
    Log.e('Error scheduling daily prayer updates: $e');
  }
}

/// Schedule prayer times for a specific day using android_alarm_manager_plus
// Future<void> _schedulePrayerTimesForDate(CalendarController calendarController,
//     DateTime date, String dayLabel) async {
//   try {
//     final dayData = calendarController.calendarByDate(date);
//     final prayerTimes = dayData.prayerTimes;
//     final now = DateTime.now();
//
//     // List of prayer times to schedule
//     final prayers = [
//       {'name': 'fajr', 'time': prayerTimes.fajr},
//       {'name': 'sunrise', 'time': prayerTimes.sunrise},
//       {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
//       {'name': 'asr', 'time': prayerTimes.asr},
//       {'name': 'maghrib', 'time': prayerTimes.maghrib},
//       {'name': 'isha', 'time': prayerTimes.isha},
//     ];
//
//     for (final prayer in prayers) {
//       try {
//         final timeString = prayer['time'] as String;
//         if (timeString.isEmpty) continue;
//
//         final timeParts = timeString.split(':');
//         if (timeParts.length < 2) continue;
//
//         final hour = int.parse(timeParts[0]);
//         final minute = int.parse(timeParts[1]);
//         final prayerDateTime =
//             DateTime(date.year, date.month, date.day, hour, minute);
//
//         // Only schedule if the prayer time is in the future
//         if (prayerDateTime.isAfter(now)) {
//           final alarmId = _createAlarmId(date, prayers.indexOf(prayer));
//           await _scheduleAlarmAt(
//               alarmId, prayerDateTime, prayerTimeUpdateCallback);
//           Log.i(
//               'Scheduled ${prayer['name']} for $dayLabel at ${prayer['time']}');
//         }
//       } catch (e) {
//         Log.e(
//             'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
//       }
//     }
//   } catch (e) {
//     Log.e('Error scheduling prayer times for $dayLabel: $e');
//   }
// }

/// Schedule prayer times for a future day (used by scheduleAllFuturePrayerUpdates)
// Future<int> _schedulePrayerTimesForFutureDate(
//     DayModel day, DateTime dayDate) async {
//   int alarmsScheduled = 0;
//
//   try {
//     final prayerTimes = day.prayerTimes;
//     final now = DateTime.now();
//
//     // List of prayer times to schedule
//     final prayers = [
//       {'name': 'fajr', 'time': prayerTimes.fajr},
//       {'name': 'sunrise', 'time': prayerTimes.sunrise},
//       {'name': 'dhuhr', 'time': prayerTimes.dhuhr},
//       {'name': 'asr', 'time': prayerTimes.asr},
//       {'name': 'maghrib', 'time': prayerTimes.maghrib},
//       {'name': 'isha', 'time': prayerTimes.isha},
//     ];
//
//     for (final prayer in prayers) {
//       try {
//         final timeString = prayer['time'] as String;
//         if (timeString.isEmpty) continue;
//
//         final timeParts = timeString.split(':');
//         if (timeParts.length < 2) continue;
//
//         final hour = int.parse(timeParts[0]);
//         final minute = int.parse(timeParts[1]);
//         final prayerDateTime =
//             DateTime(dayDate.year, dayDate.month, dayDate.day, hour, minute);
//
//         // Only schedule if the prayer time is in the future
//         if (prayerDateTime.isAfter(now)) {
//           final alarmId = _createAlarmId(dayDate, prayers.indexOf(prayer));
//           await _scheduleAlarmAt(
//               alarmId, prayerDateTime, prayerTimeUpdateCallback);
//           alarmsScheduled++;
//           Log.i(
//               'Scheduled ${prayer['name']} for ${dayDate.toString().split(' ')[0]} at ${prayer['time']}');
//         }
//       } catch (e) {
//         Log.e(
//             'Error parsing prayer time ${prayer['name']}: ${prayer['time']} - $e');
//       }
//     }
//   } catch (e) {
//     Log.e('Error scheduling prayer times for future date: $e');
//   }
//
//   return alarmsScheduled;
// }

/// Schedule an alarm at a specific time using android_alarm_manager_plus
// Future<void> _scheduleAlarmAt(
//     int alarmId, DateTime scheduledTime, Function callback) async {
//   try {
//     await AndroidAlarmManager.oneShotAt(
//       scheduledTime,
//       alarmId,
//       callback,
//       exact: true,
//       wakeup: true,
//       alarmClock: true,
//       rescheduleOnReboot: true,
//     );
//
//     Log.i('Scheduled_alarm $alarmId for ${scheduledTime.toString()}');
//   } catch (e) {
//     Log.e('Error_scheduling alarm $alarmId: $e');
//
//     // If we hit the alarm limit, throw a more specific error
//     if (e.toString().contains('Maximum limit of concurrent alarms')) {
//       throw Exception('Alarm limit reached: ${e.toString()}');
//     }
//     rethrow;
//   }
// }
//
// /// Create a unique alarm ID based on date and prayer index
// int _createAlarmId(DateTime date, int prayerIndex) {
//   // Create unique ID: YYYYMMDDHH where HH is prayer index (0-5)
//   final year = date.year % 100; // Last 2 digits of year
//   final month = date.month;
//   final day = date.day;
//
//   // Format: YYMMDDHH where HH is prayer index
//   return (year * 1000000) + (month * 10000) + (day * 100) + prayerIndex;
// }

/// Cancel all scheduled alarms (simplified for sequential scheduling)
// Future<void> cancelAllScheduledAlarms() async {
//   if (Platform.isAndroid) {
//     try {
//       // Cancel the two alarms we use in sequential scheduling
//       await AndroidAlarmManager.cancel(AlarmConfig.nextPrayerAlarmId);
//       await AndroidAlarmManager.cancel(AlarmConfig.midnightAlarmId);
//
//       // Also cancel any old alarms that might exist from previous versions
//       for (int i = 1; i <= 10; i++) {
//         await AndroidAlarmManager.cancel(i);
//       }
//       await AndroidAlarmManager.cancel(999999); // Old midnight alarm ID
//
//       Log.i('All scheduled alarms cancelled (sequential scheduling)');
//     } catch (e) {
//       Log.e('Error cancelling alarms: $e');
//     }
//   }
// }

/// Cancel all scheduled alarms (for 2-day rolling scheduling)
Future<void> cancelAllScheduledAlarms() async {
  if (Platform.isAndroid) {
    try {
      // Cancel midnight alarm
      await AndroidAlarmManager.cancel(AlarmConfig.midnightAlarmId);

      // Cancel prayer alarms for today and tomorrow (and a few extra days for safety)
      final now = DateTime.now();
      for (int dayOffset = -1; dayOffset <= 3; dayOffset++) {
        final date = now.add(Duration(days: dayOffset));
        for (int prayerIndex = 0; prayerIndex < 6; prayerIndex++) {
          final alarmId = _createAlarmId(date, prayerIndex);
          await AndroidAlarmManager.cancel(alarmId);
        }
      }

      // Also cancel any old alarms that might exist from previous versions
      for (int i = 1; i <= 10; i++) {
        await AndroidAlarmManager.cancel(i);
      }

      Log.i('All scheduled alarms cancelled (2-day rolling scheduling)');
    } catch (e) {
      Log.e('Error cancelling alarms: $e');
    }
  }
}

/// Check if we should schedule the next 2-day batch
Future<bool> _shouldScheduleNextBatch() async {
  final now = DateTime.now();
  final tomorrow = DateTime(now.year, now.month, now.day + 1);

  // If it's past 6 PM today or it's already tomorrow, schedule next batch
  if (now.hour >= 18 || now.isAfter(tomorrow)) {
    return true;
  }

  // If it's midnight alarm (hour 0), always schedule next batch
  if (now.hour == 0 && now.minute <= 5) {
    return true;
  }

  return false;
}
