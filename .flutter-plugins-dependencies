{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service-0.18.18/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "eraser", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eraser-3.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_compass_v2", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_compass_v2-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_exit_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_exit_app-1.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_qiblah", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_qiblah-3.1.0+1/", "native_build": true, "dependencies": ["flutter_compass_v2"], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "home_widget", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/home_widget-0.8.0/", "native_build": true, "dependencies": ["path_provider_foundation"], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.10.4/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_ios", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "android": [{"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service-0.18.18/", "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "eraser", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/eraser-3.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "flutter_compass_v2", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_compass_v2-1.0.3/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_exit_app", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_exit_app-1.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "flutter_qiblah", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_qiblah-3.1.0+1/", "native_build": true, "dependencies": ["flutter_compass_v2"], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "home_widget", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/home_widget-0.8.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.10.4/", "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_android", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "macos": [{"name": "audio_service", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service-0.18.18/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.9/", "native_build": true, "dependencies": ["firebase_core"], "dev_dependency": false}, {"name": "geolocator_apple", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "just_audio", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.10.4/", "shared_darwin_source": true, "native_build": true, "dependencies": ["audio_session"], "dev_dependency": false}, {"name": "location", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location-8.0.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_foundation", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "sqflite_darwin", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/", "shared_darwin_source": true, "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": true, "dependencies": ["package_info_plus"], "dev_dependency": false}], "linux": [{"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": false, "dependencies": ["url_launcher_linux"], "dev_dependency": false}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "windows": [{"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "firebase_core", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.15.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "geolocator_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "path_provider_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/", "native_build": false, "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "native_build": true, "dependencies": ["url_launcher_windows"], "dev_dependency": false}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/", "native_build": true, "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "native_build": false, "dependencies": ["package_info_plus"], "dev_dependency": false}], "web": [{"name": "audio_service_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service_web-0.1.4/", "dependencies": [], "dev_dependency": false}, {"name": "audio_session", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.2.2/", "dependencies": [], "dev_dependency": false}, {"name": "awesome_notifications", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/", "dependencies": [], "dev_dependency": false}, {"name": "connectivity_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_core_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.1/", "dependencies": [], "dev_dependency": false}, {"name": "firebase_messaging_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.9/", "dependencies": ["firebase_core_web"], "dev_dependency": false}, {"name": "fluttertoast", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.12/", "dependencies": [], "dev_dependency": false}, {"name": "geolocator_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/", "dependencies": [], "dev_dependency": false}, {"name": "just_audio_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/", "dependencies": [], "dev_dependency": false}, {"name": "location_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/location_web-6.0.1/", "dependencies": [], "dev_dependency": false}, {"name": "package_info_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/", "dependencies": [], "dev_dependency": false}, {"name": "permission_handler_html", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/", "dependencies": [], "dev_dependency": false}, {"name": "share_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-11.0.0/", "dependencies": ["url_launcher_web"], "dev_dependency": false}, {"name": "url_launcher_web", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/", "dependencies": [], "dev_dependency": false}, {"name": "wakelock_plus", "path": "/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/", "dependencies": ["package_info_plus"], "dev_dependency": false}]}, "dependencyGraph": [{"name": "audio_service", "dependencies": ["audio_service_web", "audio_session"]}, {"name": "audio_service_web", "dependencies": []}, {"name": "audio_session", "dependencies": []}, {"name": "awesome_notifications", "dependencies": []}, {"name": "connectivity_plus", "dependencies": []}, {"name": "eraser", "dependencies": []}, {"name": "firebase_core", "dependencies": ["firebase_core_web"]}, {"name": "firebase_core_web", "dependencies": []}, {"name": "firebase_messaging", "dependencies": ["firebase_core", "firebase_messaging_web"]}, {"name": "firebase_messaging_web", "dependencies": ["firebase_core", "firebase_core_web"]}, {"name": "flutter_compass_v2", "dependencies": []}, {"name": "flutter_exit_app", "dependencies": []}, {"name": "flutter_qiblah", "dependencies": ["flutter_compass_v2", "geolocator"]}, {"name": "fluttertoast", "dependencies": []}, {"name": "geolocator", "dependencies": ["geolocator_android", "geolocator_apple", "geolocator_web", "geolocator_windows"]}, {"name": "geolocator_android", "dependencies": []}, {"name": "geolocator_apple", "dependencies": []}, {"name": "geolocator_web", "dependencies": []}, {"name": "geolocator_windows", "dependencies": []}, {"name": "home_widget", "dependencies": ["path_provider", "path_provider_foundation"]}, {"name": "just_audio", "dependencies": ["just_audio_web", "audio_session", "path_provider"]}, {"name": "just_audio_web", "dependencies": []}, {"name": "location", "dependencies": ["location_web"]}, {"name": "location_web", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_windows"]}, {"name": "path_provider_android", "dependencies": []}, {"name": "path_provider_foundation", "dependencies": []}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "permission_handler", "dependencies": ["permission_handler_android", "permission_handler_apple", "permission_handler_html", "permission_handler_windows"]}, {"name": "permission_handler_android", "dependencies": []}, {"name": "permission_handler_apple", "dependencies": []}, {"name": "permission_handler_html", "dependencies": []}, {"name": "permission_handler_windows", "dependencies": []}, {"name": "share_plus", "dependencies": ["url_launcher_web", "url_launcher_windows", "url_launcher_linux"]}, {"name": "sqflite", "dependencies": ["sqflite_android", "sqflite_darwin"]}, {"name": "sqflite_android", "dependencies": []}, {"name": "sqflite_darwin", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_android", "url_launcher_ios", "url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_android", "dependencies": []}, {"name": "url_launcher_ios", "dependencies": []}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}, {"name": "wakelock_plus", "dependencies": ["package_info_plus"]}], "date_created": "2025-07-07 11:52:44.204478", "version": "3.32.5", "swift_package_manager_enabled": {"ios": false, "macos": false}}